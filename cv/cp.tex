%-------------------------------------------------------------------------------
%	SECTION TITLE
%-------------------------------------------------------------------------------
\cvsection{Competitive Programming and Data Science Experience}


%-------------------------------------------------------------------------------
%	CONTENT
%-------------------------------------------------------------------------------
\begin{cventries}

%---------------------------------------------------------
    \cventry
    {Problem Solver} 
    {LeetCode} 
    {} % Location
    {2022 -- Current} % Date(s)
    {
      \begin{cvitems} % Description(s) of project
        % I have solved over 340 problems on LeetCode.
        % \item 
        \item {\textbf{Handle Name:} \href{https://leetcode.com/golammostofa10001/}{{golammostofa10001}}}
       % \item {\textbf{Advanced Skills:} Dynamic Programming, Divide and Conquer, Backtracking, Union Find, Monotonic Stack, Trie.}
        %\item {\textbf{Intermediate Skills:} Math, Hash Table, Tree, Binary Tree, Depth-First Search, Bit Manipulation, Binary Search, Breadth-First Search.}
        %\item {\textbf{Fundamental Skills:} Array, String, Two Pointers, Sorting, Linked List, Simulation, Matrix, Stack.}
        \item {\textbf{Maximum Contest Rating:} 1433}
      \end{cvitems}
    }


    %_____________________________________________

    \cventry
    {Kaggle Notebooks Expert} 
    {Kaggle} 
    {} % Location
    {2022 -- Current} % Date(s)
    {
      \begin{cvitems} % Description(s) of project
        % I enhanced my data science, Machine Learning, and NLP skills through Kaggle competitions.
        % \item 
        \item {\textbf{Handle Name:} \href{https://www.kaggle.com/golammostofas}{{golammostofas}}}
       % \item {\textbf{Advanced Skills:} Dynamic Programming, Divide and Conquer, Backtracking, Union Find, Monotonic Stack, Trie.}
        %\item {\textbf{Intermediate Skills:} Math, Hash Table, Tree, Binary Tree, Depth-First Search, Bit Manipulation, Binary Search, Breadth-First Search.}
        %\item {\textbf{Fundamental Skills:} Array, String, Two Pointers, Sorting, Linked List, Simulation, Matrix, Stack.}
        \item {\textbf{Maximum Competition Topper:} I am top 5\% (44/955) on \href{https://www.kaggle.com/competitions/playground-series-s3e21/leaderboard}{{"Improve a Fixed Model the Data-Centric Way!"}} competition.}
      \end{cvitems}
    }


    %_____________________________________________
\iffalse

    \cventry
    {Online Judge} 
    {CodeForces} 
    {} % Location
    {2017 -- Current} % Date(s)
    {
      \begin{cvitems} % Description(s) of project
        \item {I have solved over 50 problems on CodeForces.
        }
        \item {\textbf{Handle Name:} \href{https://codeforces.com/profile/mostofa}{\underline{mostofa}}}
       % \item {\textbf{Advanced Skills:} Dynamic Programming, Divide and Conquer, Backtracking, Union Find, Monotonic Stack, Trie.}
        %\item {\textbf{Intermediate Skills:} Math, Hash Table, Tree, Binary Tree, Depth-First Search, Bit Manipulation, Binary Search, Breadth-First Search.}
        %\item {\textbf{Fundamental Skills:} Array, String, Two Pointers, Sorting, Linked List, Simulation, Matrix, Stack.}
        \item {\textbf{Maximum Contest Rating:} 784}
      \end{cvitems}
    }

\fi

\end{cventries}
