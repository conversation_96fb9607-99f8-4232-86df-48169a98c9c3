@inproceedings{mD<PERSON><PERSON><PERSON>,
author = {<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and R<PERSON>d, <PERSON>b<PERSON> and <PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON>},
title = {M-DBSCAN: Modified DBSCAN Clustering Algorithm for Detecting and Controlling Outliers},
year = {2024},
isbn = {9798400702433},
publisher = {Association for Computing Machinery},
address = {New York, NY, USA},
url = {https://doi.org/10.1145/3605098.3636188},
doi = {10.1145/3605098.3636188},
abstract = {Outlier reduction is crucial in computer science for improving data quality, analysis accuracy, and modeling robustness. Selection and modification of DBSCAN parameters are essential for optimal clustering accuracy and outlier detection. We developed an adaptive technique to minimize outliers in the DBSCAN algorithm using a linear congruential method (LCM) to determine values of Epsilon (Eps) and Min-Points (MinPts), known as modified DBSCAN (M-DBSCAN). To enhance the DBSCAN method, we create integer random numbers for MinPts (1--100) and floating numbers for Eps (0.1--1.5) using LCM. We adjusted parameter lists to reduce outliers based on MinPts and Eps values. We choose parameters based on dataset features and requirements, balancing clustering sensitivity and noise treatment. For experiment result analysis we use the Silhouette Score (SS) method. M-DBSCAN improved all cases and it has 50\% poorer outlier accuracy than DBSCAN.},
booktitle = {Proceedings of the 39th ACM/SIGAPP Symposium on Applied Computing},
pages = {1034–1035},
numpages = {2},
keywords = {data clustering, outlier handling, DBSCAN, M-DBSCAN},
location = {Avila, Spain},
series = {SAC '24}
}
