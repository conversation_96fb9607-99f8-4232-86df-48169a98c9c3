%-------------------------------------------------------------------------------
%	SECTION TITLE
%-------------------------------------------------------------------------------
\cvsection{Core Skills}


%-------------------------------------------------------------------------------
%	CONTENT
%-------------------------------------------------------------------------------
\begin{cvskills}

%---------------------------------------------------------
  \cvskill
    {Programming Language} % Category
    {Python and C/C++, and JavaScript.}
    %{Python (Pandas, PyTorch, NumPy, Scikit‑learn.), R(ggplot2), PHP, C/C++, HTML/CSS, JavaScript, SQL.} % Skills

  \cvskill
    {LLM Models} % Category
    {GPT-4o, LLama3.2, Mixtral, Phi-3, Llava.}
    %{Python (Pandas, PyTorch, NumPy, Scikit‑learn.), R(ggplot2), PHP, C/C++, HTML/CSS, JavaScript, SQL.} % Skills

\cvskill
    {Generative AI} % Category
    {LLM Build, Fine-Tuning, RAG, LoRA, QLoRa, LangChain, Prompt Engineering,  Mem0, Ollama, NLP.}
    %{Python (Pandas, PyTorch, NumPy, Scikit‑learn.), R(ggplot2), PHP, C/C++, HTML/CSS, JavaScript, SQL.} % Skills

 \cvskill
    {Machine Learning} % Category
    {Deep Learning, CNN, ANN, Perception, TensorFlow, PyTorch, Open‑CV, DeepFace, etc.}
    %{Python (Pandas, PyTorch, NumPy, Scikit‑learn.), R(ggplot2), PHP, C/C++, HTML/CSS, JavaScript, SQL.} % Skills

 \cvskill
    {Big Data} % Category
    {PySpark, Dask, Hadoop, Kafka, Pandas}
    %{Python (Pandas, PyTorch, NumPy, Scikit‑learn.), R(ggplot2), PHP, C/C++, HTML/CSS, JavaScript, SQL.} % Skills


 \cvskill
    {Web Frameworks} % Category
    {Django, REST API, Simple JWT, dj-rest-auth, django-allauth, FastAPI, Oauth-2, and HTML-5, Axious.}


%---------------------------------------------------------
 \cvskill
    {CSS Frameworks} % Category
    {Tailwind and Flowbite - (Basic).}
    %{Python (Pandas, PyTorch, NumPy, Scikit‑learn.), R(ggplot2), PHP, C/C++, HTML/CSS, JavaScript, SQL.} % Skills
    
 % \cvskill
 %    {Desktop Frameworks} % Category
 %    {Kivy, KivyMD}

%---------------------------------------------------------
 \cvskill
    {Database} % Category
    {ChromaDB, ORM, My-SQL and MongoDB.} % Skills
%---------------------------------------------------------
 \cvskill
    {Version Control} % Category
    {Git and GitHub} % Skills
 \cvskill
    {Cloud Computing}
    {AWS(EC2, S3), Azure(VM, web App), Vercel, Railway, Rander, PythonAnyWhere}

 \cvskill
    {Other Technologies}
    {Docker, Nginx}

 % \cvskill
 %    {Operating System}
 %    {Linux, Mac, Windows}

\end{cvskills}
