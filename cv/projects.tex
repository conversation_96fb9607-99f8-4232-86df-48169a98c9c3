%-------------------------------------------------------------------------------
%	SECTION TITLE
%-------------------------------------------------------------------------------
\cvsection{Personal Projects}


%-------------------------------------------------------------------------------
%	CONTENT
%-------------------------------------------------------------------------------
\begin{cventries}


% --------------------------------------

 % \cventry
    % {Serenus One}
    % {\href{https://app.serenus.one/}{\faGlobe{ \underline{Sara's AI Assistant}}}}
    % {}
    % {Current}
    % {
    %     \begin{cvitems}
    %         This is an LLM-based AI assistant that can perform mental health. It is fintuned from ChatGPT 4o-mini, Llama3.2, and Claude.
    %         \item 
    %         \item {\textbf{Technical Skills:} Fine Tuning, LiteLLM, Mem0, FastAPI, Langchain}
    %     \end{cvitems}
    % }

% --------------------------------------

 % \cventry
 %    {Devolved AI}
 %    {\href{https://app.devolvedai.com/chat}{\faGlobe{ \underline{Athena LLM}}}}
 %    {}
 %    {Current}
 %    {
 %        \begin{cvitems}
 %            Athena is a large language model, which fintuned Llama-3.1 8b instruction LLM.
 %            \item 
 %            \item {\textbf{Technical Skills:} Fine Tuning, Federated Learning, FastAPI, Ollama}
 %        \end{cvitems}
 %    }


% --------------------------------------

 \cventry
    {}
    {\href{https://github.com/shuvo881/RAG-System}{{ {RAG System}}}}
    {}
    {Dec, 2024}
    {
        \begin{cvitems}
            Built a retrieval-augmented generation system using LangChain framework to enhance LLM responses with relevant context from indexed documents.
            \item 
            \item {\textbf{Technical Skills:} LangChain, OpenAI API, Ollama}
        \end{cvitems}
    }

% --------------------------------------

 \cventry
    {}
    {\href{https://www.kaggle.com/models/golammostofas/astra/}{{ {Astra LLM}}}}
    {}
    {Oct, 2024}
    {
        \begin{cvitems}
            Developed a 1.4B parameter base large language model, establishing core NLP capabilities without the need for additional foundation models.
            \item 
            \item {\textbf{Technical Skills:} Build LLM from Scratch, PyTorch, LLM Architecture }
        \end{cvitems}
    }







% --------------------------------------

 % \cventry
 %    {}
 %    {\href{https://github.com/shuvo881/Automatic-MCQ-Generate}{{ {Automatic MCQ Generator}}}}
 %    {}
 %    {Sept, 2024}
 %    {
 %        \begin{cvitems}
 %            Built a system that automatically generates multiple-choice questions from educational content, including questions, correct answers, and plausible distractors using NLP techniques.
 %            \item 
 %            \item {\textbf{Technical Skills:} LangChain, Gemini API}
 %        \end{cvitems}
 %    }



%---------------------------------------------------------

    % \cventry
    % {SK BD}  % Organisation
    % {\href{http://************:8000/}{\faGlobe{ \underline{SK Best Quality}}}} % Project
    % {} % Location
    % {February 2024} % Date(s)
    % {
    %   \begin{cvitems} % Description(s) of project 
    %     \item {\textbf{Technical Skills:} Django, REST-API, EC2, Docker, Postgre-SQL, Deployment, JavaScript, Tailwind CSS, and HTML.}
    %   \end{cvitems}
    % }

%---------------------------------------------------------

    % \cventry
    % {Self Study} % Organisation
    % {\href{https://github.com/shuvo881/Urgent_Online_Marketplace}{\faGithub { \underline{Urgent Online Market Place}}}} % Project
    % {} % Location
    % {October 2023} % Date(s)
    % {
    %   \begin{cvitems} % Description(s) of project
    %     \item {\textbf{Technical Skills:} Django, Postgre-SQL, EC2, Docker, Deployment, JavaScript, Tailwind CSS, and HTML.}
    %   \end{cvitems}
    % }


%--------------------------------------

  % \cventry
  %   {Self Study} % Organisation
  %   {\href{https://github.com/shuvo881/AI_Chat_Bot}{\faGithub{ \underline{AI Chat Bot}}}} % Project
  %   {} % Location
  %   {Sept 2023} % Date(s)
  %   {
  %     \begin{cvitems} % Description(s) of project
  %       \item {\textbf{Technical Skills:} Django, REST-API, React JS, CSS, HTML.}
  %     \end{cvitems}
  %   }


%---------------------------------------------------------

    % \cventry
    % {Self Study} % Organisation
    % {\href{https://agricultural-ai-doctor.onrender.com/}{\faGlobe{ \underline{Agricultural AI Doctor}}}}  % Project
    % {} % Location
    % {Aug 2023} % Date(s)
    % {
    %     \begin{cvitems} % Description(s) of project.
    %         \item {\textbf{Technical Skills:} Machine Learning, Django, Bootstrap CSS, HTLM-5, ORM, and Deployment.}
    %     \end{cvitems}
    % }

%---------------------------------------------------------


  % \cventry
  %   {DUET Robotics Club - DRC} % Organisation
  %   {\href{https://github.com/shuvo881/Face-Mask-Detect-Door-System}{\faGithub { \underline{Face Mask Detect Door System}}}} % Project
  %   {DUET, Gazipur} % Location
  %   {Mar 2022} % Date(s)
  %   {
  %     \begin{cvitems} % Description(s) of project
  %       \item {\textbf{Technical Skills:} Supervised Machine Learning, TensorFlow, Keras, Open-cv}
  %     \end{cvitems}
  %   }


%---------------------------------------------------------
  % \cventry
  %   {Project showcasing at DUET} % Organisation
  %   {\href{https://github.com/shuvo881/Emusic}{\faGithub{ \underline{Emotion Based Music System}}}} % Project
  %   {DUET, Gazipur} % Location
  %   {Dec 2021} % Date(s)
  %   {
  %     \begin{cvitems} % Description(s) of project
  %       \item {\textbf{Technical Skills:} DeepFace, CV2.}
  %     \end{cvitems}
  %   }

%---------------------------------------------------------
    % \cventry
    % {Project showcasing at DUET} % Organisation
    % {\href{https://github.com/shuvo881/DUET-Hall-Dining-Management-System}{\faGithub{ \underline{DUET Hall Dining Management System}}}} % Project
    % {DUET, Gazipur} % Location
    % {Jun 2021} % Date(s)
    % {
    %   \begin{cvitems} % Description(s) of project
    %     \item {\textbf{Technical Skills:} .NET Framework, MS-SQL and C\#}
    %   \end{cvitems}
    % }
%-------------------------------------------------------



\end{cventries}
