
%-------------------------------------------------------------------------------
% CONFIGURATIONS
%-------------------------------------------------------------------------------
% A4 paper size by default, use 'letterpaper' for US letter
\documentclass[11pt, a4paper]{russell}
\usepackage{fontawesome5}
\usepackage{hyperref}
\usepackage[super]{nth}
\usepackage{fontspec}
\usepackage{xcolor} 
\usepackage{ulem} % For underlining
\usepackage{multicol} % Package for multiple columns

% Configure page margins with geometry
\geometry{left=1.4cm, top=.8cm, right=1.4cm, bottom=1.8cm, footskip=.5cm}

% Specify the location of the included fonts
\fontdir[fonts/]
\defaultfontfeatures{Ligatures=TeX}

% Define stylish, smart colors using hex codes or RGB
\definecolor{smartblue}{HTML}{1A73E8}  % A more professional blue
\definecolor{smartgreen}{HTML}{34A853} % A pleasing green
\definecolor{smartred}{HTML}{EA4335}   % A modern red
\definecolor{smartpurple}{HTML}{8E24AA} % A soft purple for citations

% Color for highlights
% russell Colors: russell-emerald, russell-skyblue, russell-red, russell-pink, russell-orange
%                 russell-nephritis, russell-concrete, russell-darknight, russell-purple
\colorlet{russell}{black}
% Uncomment if you would like to specify your own color
% \definecolor{russell}{HTML}{CA63A8}

% Colors for text
% Uncomment if you would like to specify your own color
\definecolor{darktext}{HTML}{414141}
% \definecolor{text}{HTML}{333333}
% \definecolor{graytext}{HTML}{5D5D5D}
% \definecolor{lighttext}{HTML}{999999}

% Set false if you don't want to highlight section with russell color
\setbool{acvSectionColorHighlight}{true}

% If you would like to change the social information separator from a pipe (|) to something else
\renewcommand{\acvHeaderSocialSep}{\quad\textbar\quad}

% save the original href command in a new command:
\let\hrefWithoutArrow\href
% new command for external links:
\renewcommand{\href}[2]{\hrefWithoutArrow{#1}{\ifthenelse{\equal{#2}{}}{ }{#2 }\raisebox{.15ex}{\footnotesize \faExternalLink*}}}

% Set up hyperlink colors with style
\hypersetup{
    colorlinks=true,     % Enable colored links
    urlcolor=smartblue,  % URL color - stylish blue
    linkcolor=smartgreen, % Internal link color - professional green
    citecolor=smartpurple % Citation link color - modern purple
}

\urlstyle{same} % Ensure URL style follows the document's main style

%-------------------------------------------------------------------------------
%	PERSONAL INFORMATION
%	Comment any of the lines below if they are not required
%-------------------------------------------------------------------------------
% Available options: circle|rectangle,edge/noedge,left/right
% \photo[circle,edge,right]{300 300-removebg-preview.jpg}
\name{{Md. Golam}}{Mostofa}
 \position{Machine Learning Engineer{\enskip\cdotp\enskip}Python Backend Developer{\enskip\cdotp\enskip} Competitive Programmer {\enskip\cdotp\enskip} Kaggle Expert}
\address{\hspace{1mm} Dhaka, Bangladesh}

\mobile{(+880) 1780 739 705}
\email{<EMAIL>}
% \dateofbirth{August, 1997}
\homepage{shuvo881.github.io} 
\github{shuvo881}
\linkedin{mdgolammostofa705}
% \gitlab{gitlab-id}
% \stackoverflow{SO-id}{SO-name}
% \twitter{@twit}
% \skype{https://join.skype.com/invite/XFqAGRDxKEYr}
% \reddit{reddit-id}
 % \medium{golammostofa10001}
% \orcid{0009-0000-4664-669X}
% \kaggle{golammostofas}
\googlescholar{mxW1qOoAAAAJ&hl}{}
%% \firstname and \lastname will be used
% \googlescholar{googlescholar-id}{}
% \extrainfo{extra information}

 \quote{"No system is safe, so be careful"}


%-------------------------------------------------------------------------------



 %-------------------------------------------------------------------------------
\addbibresource{cv/references.bib}

%-------------------------------------------------------------------------------
\begin{document}

% Print the header with above personal informations
% Give optional argument to change alignment(C: center, L: left, R: right)
\makecvheader
% Print the footer with 3 arguments(<left>, <center>, <right>)
% Leave any of these blank if they are not needed
\makecvfooter
  {\today}
  {}
  {\thepage}

%-------------------------------------------------------------------------------
%	CV/RESUME CONTENT
%	Each section is imported separately, open each file in turn to modify content
%-------------------------------------------------------------------------------
\input{cv/summary.tex}
\input{cv/experience.tex}
\input{cv/cp.tex}
\input{cv/education.tex}
\input{cv/publications.tex}
\input{cv/projects.tex}
% \input{cv/Licenses & certifications.tex}
% \input{cv/achievements.tex}
\input{cv/skills.tex}
% \input{cv/Extra-Curricular Activities.tex}


\input{cv/interests.tex}
\input{cv/languages.tex}
\input{cv/Reference.tex}

\vspace*{\fill}
%\centering{\textbf{References available upon request.}}
%-------------------------------------------------------------------------------
\end{document}
