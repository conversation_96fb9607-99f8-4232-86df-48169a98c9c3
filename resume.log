This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023/Debian) (preloaded format=pdflatex 2025.10.3)  3 OCT 2025 00:43
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**resume.tex
(./resume.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-22>
(./russell.cls
Document Class: russell 2017/02/05 v1.6.1 russell Curriculum Vitae Class
(/usr/share/texlive/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(/usr/share/texlive/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count187
\c@section=\count188
\c@subsection=\count189
\c@subsubsection=\count190
\c@paragraph=\count191
\c@subparagraph=\count192
\c@figure=\count193
\c@table=\count194
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
)
(/usr/share/texlive/texmf-dist/tex/latex/tools/array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen141
\ar@mcellbox=\box51
\extrarowheight=\dimen142
\NC@list=\toks17
\extratabsurround=\skip50
\backup@length=\skip51
\ar@cellbox=\box52
)
(/usr/share/texlive/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\enitkv@toks@=\toks18
\labelindent=\skip52
\enit@outerparindent=\dimen143
\enit@toks=\toks19
\enit@inbox=\box53
\enit@count@id=\count195
\enitdp@description=\count196
)
(/usr/share/texlive/texmf-dist/tex/latex/ragged2e/ragged2e.sty
Package: ragged2e 2023/06/22 v3.6 ragged2e Package
\CenteringLeftskip=\skip53
\RaggedLeftLeftskip=\skip54
\RaggedRightLeftskip=\skip55
\CenteringRightskip=\skip56
\RaggedLeftRightskip=\skip57
\RaggedRightRightskip=\skip58
\CenteringParfillskip=\skip59
\RaggedLeftParfillskip=\skip60
\RaggedRightParfillskip=\skip61
\JustifyingParfillskip=\skip62
\CenteringParindent=\skip63
\RaggedLeftParindent=\skip64
\RaggedRightParindent=\skip65
\JustifyingParindent=\skip66
)
(/usr/share/texlive/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks20
)
(/usr/share/texlive/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(/usr/share/texlive/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count197
\Gm@cntv=\count198
\c@Gm@tempcnt=\count199
\Gm@bindingoffset=\dimen144
\Gm@wd@mp=\dimen145
\Gm@odd@mp=\dimen146
\Gm@even@mp=\dimen147
\Gm@layoutwidth=\dimen148
\Gm@layoutheight=\dimen149
\Gm@layouthoffset=\dimen150
\Gm@layoutvoffset=\dimen151
\Gm@dimlist=\toks21
)
(/usr/share/texlive/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2022/11/09 v4.1 Extensive control of page headers and footers

\f@nch@headwidth=\skip67
\f@nch@O@elh=\skip68
\f@nch@O@erh=\skip69
\f@nch@O@olh=\skip70
\f@nch@O@orh=\skip71
\f@nch@O@elf=\skip72
\f@nch@O@erf=\skip73
\f@nch@O@olf=\skip74
\f@nch@O@orf=\skip75
)
(/usr/share/texlive/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(/usr/share/texlive/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)
(/usr/share/texlive/texmf-dist/tex/generic/iftex/ifxetex.sty
Package: ifxetex 2019/10/25 v0.7 ifxetex legacy package. Use iftex instead.
)
(/usr/share/texlive/texmf-dist/tex/latex/xifthen/xifthen.sty
Package: xifthen 2015/11/05 v1.4.0 Extended ifthen features

(/usr/share/texlive/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count266
\calc@Bcount=\count267
\calc@Adimen=\dimen152
\calc@Bdimen=\dimen153
\calc@Askip=\skip76
\calc@Bskip=\skip77
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count268
\calc@Cskip=\skip78
)
(/usr/share/texlive/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/ifmtarg/ifmtarg.sty
Package: ifmtarg 2018/04/16 v1.2b check for an empty argument
))
(/usr/share/texlive/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count269
)
(/usr/share/texlive/texmf-dist/tex/latex/setspace/setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
)
(/usr/share/texlive/texmf-dist/tex/latex/fontspec/fontspec.sty
(/usr/share/texlive/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
(/usr/share/texlive/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-01-22 L3 programming layer (loader) 

(/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-01-04 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count270
\l__pdf_internal_box=\box54
))
Package: xparse 2023-10-10 L3 Experimental document command parser
)
Package: fontspec 2022/01/15 v2.8a Font selection for XeLaTeX and LuaLaTeX


! Fatal Package fontspec Error: The fontspec package requires either XeTeX or
(fontspec)                      LuaTeX.
(fontspec)                      
(fontspec)                      You must change your typesetting engine to,
(fontspec)                      e.g., "xelatex" or "lualatex" instead of
(fontspec)                      "latex" or "pdflatex".

Type <return> to continue.
 ...                                              
                                                  
l.45 \msg_fatal:nn {fontspec} {cannot-use-pdftex}
                                                 
? 

! Emergency stop.
<read *> 
         
l.45 \msg_fatal:nn {fontspec} {cannot-use-pdftex}
                                                 
*** (cannot \read from terminal in nonstop modes)

 
Here is how much of TeX's memory you used:
 3013 strings out of 474222
 48516 string characters out of 5748733
 1922975 words of memory out of 5000000
 25272 multiletter control sequences out of 15000+600000
 558369 words of font info for 37 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 94i,0n,107p,269b,42s stack positions out of 10000i,1000n,20000p,200000b,200000s
!  ==> Fatal error occurred, no output PDF file produced!
